from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Type
from pydantic import ValidationError
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from core.logging.logger_config import get_module_logger
import asyncio
from schemas.layer2_schema import (
    STTInputSchema, STTOutputSchema,
    PreProcessingInputSchema, PreProcessingOutputSchema,
    ProcessingInputSchema, ProcessingOutputSchema,
    FillerInputSchema, FillerOutputSchema,
    TTSInputSchema, TTSOutputSchema
)

"""
Pipeline States v2
------------------
This module defines the abstract base class and concrete pipeline state classes for the new state-based pipeline management system.
Each state retrieves its agent from the agent registry, uses the agent's process method, and handles messaging, validation, and error handling.

Enhanced with proper Pydantic schema validation for input/output data.
"""


class AbstractPipelineState(ABC):
    """
    Abstract base class for all pipeline states.

    Provides schema validation, agent orchestration, and notification publishing.
    Each concrete state must define input_schema_class and output_schema_class.
    """
    id: str
    input_schema_class: Type = None
    output_schema_class: Type = None

    def __init__(self, state_id: str, agent_registry: Any, session_id: str):
        self.id = state_id
        self.agent_registry = agent_registry
        self.session_id = session_id
        self.logger = get_module_logger("pipeline_state", session_id=self.session_id, state_id=self.id)

    @abstractmethod
    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        pass

    # @classmethod
    # def get_input_schema(cls) -> Dict[str, Any]:
    #     """Get input schema as dictionary for compatibility"""
    #     if cls.input_schema_class:
    #         return cls.input_schema_class.model_json_schema()
    #     return {}

    # @classmethod
    # def get_output_schema(cls) -> Dict[str, Any]:
    #     """Get output schema as dictionary for compatibility"""
    #     if cls.output_schema_class:
    #         return cls.output_schema_class.model_json_schema()
    #     return {}

    def validate_input(self, input_data: Dict[str, Any]) -> Any:
        if not self.input_schema_class:
            self.logger.warning(f"No input schema defined for state {self.id}")
            return input_data
        try:
            validated_input = self.input_schema_class(**input_data)
            self.logger.debug(f"Input validation successful for state {self.id}")
            return validated_input
        except ValidationError as e:
            self.logger.error(f"Input validation failed for state {self.id}: {e}")
            raise

    def validate_output(self, output_data: Dict[str, Any]) -> Any:
        if not self.output_schema_class:
            self.logger.warning(f"No output schema defined for state {self.id}")
            return output_data
        try:
            validated_output = self.output_schema_class(**output_data)
            self.logger.debug(f"Output validation successful for state {self.id}")
            return validated_output
        except ValidationError as e:
            self.logger.error(f"Output validation failed for state {self.id}: {e}")
            raise

    async def _publish_notification(self, status: str, payload: Dict[str, Any], context_keys_updated=None, error_message=None):
        notification = A2AMessage(
            session_id=self.session_id,
            message_type=MessageType.NOTIFICATION,
            source_agent=self.id,
            target_agent="Orchestrator",
            payload={"status": status, **({"error_message": error_message} if error_message else {}), **payload},
            context_keys_updated=context_keys_updated or []
        )
        redis_client = self.agent_registry._redis
        await redis_client.publish("agent_completion", notification.to_json())


# --- Concrete Pipeline States ---

class STTState(AbstractPipelineState):
    """
    Speech-to-Text State
    Converts audio input to text transcript with latency tracking.
    """
    input_schema_class = STTInputSchema
    output_schema_class = STTOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"STTState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("stt_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"STTState output validation failed: {e}")
            await self._publish_notification("complete", {"latencySTT": result.outputs.get("latencySTT")}, ["transcript", "latencySTT"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"STTState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class PreProcessingState(AbstractPipelineState):
    """
    Preprocessing State
    Cleans text and extracts intent, emotion, and gender information.
    """
    input_schema_class = PreProcessingInputSchema
    output_schema_class = PreProcessingOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreProcessingState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("preprocessing_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"PreProcessingState output validation failed: {e}")
            await self._publish_notification("complete", {"latencyPreprocessing": result.outputs.get("latencyPreprocessing")}, ["clean_text", "intent", "emotion", "gender", "latencyPreprocessing"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreProcessingState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class ProcessingState(AbstractPipelineState):
    """
    Processing State
    Processes clean text and intent to generate LLM responses and business logic results.
    """
    input_schema_class = ProcessingInputSchema
    output_schema_class = ProcessingOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("processing_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"ProcessingState output validation failed: {e}")
            await self._publish_notification("complete", {"latencyProcessing": result.outputs.get("latencyProcessing")}, ["llm_answer", "account_balance", "loan_eligibility", "exit_signal", "latencyProcessing"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class FillerState(AbstractPipelineState):
    """
    Filler State
    Generates filler audio content for conversation flow management.
    """
    input_schema_class = FillerInputSchema
    output_schema_class = FillerOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"FillerState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("filler_tts_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"FillerState output validation failed: {e}")
            await self._publish_notification("filler", {"audio_path": result.outputs.get("audio_path"), "filler_text": result.outputs.get("filler_text")}, ["audio_path", "filler_text"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"FillerState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class TTSState(AbstractPipelineState):
    """
    Text-to-Speech State
    Converts text to speech with emotion and gender parameters.
    Works with InterruptState for interrupt handling during playback.
    """
    input_schema_class = TTSInputSchema
    output_schema_class = TTSOutputSchema

    def __init__(self, state_id: str, agent_registry, session_id: str):
        super().__init__(state_id, agent_registry, session_id)

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        """
        Process TTS generation.

        This state focuses only on TTS audio generation.
        Interrupt handling is managed by the separate InterruptState.
        """
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )

        # Get TTS agent from registry
        agent = self.agent_registry.getAgent("tts_agent")
        if not agent:
            return StateOutput(
                status=StatusType.ERROR,
                message="TTS agent not found in registry",
                code=StatusCode.NOT_FOUND,
                outputs={},
                meta={"error": "agent_not_found"}
            )

        try:
            # Generate TTS audio
            result = await agent.process(validated_input.model_dump(), context)

            if result.status != StatusType.SUCCESS:
                return result

            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"TTSState output validation failed: {e}")

            await self._publish_notification("complete", {"latencyTTS": result.outputs.get("latencyTTS")}, ["audio_path", "latencyTTS"])
            return result

        except Exception as e:
            self.logger.error(
                "Error in TTS state processing",
                action="process",
                reason=str(e),
                layer="tts_state"
            )
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )



class InterruptState(AbstractPipelineState):
    """
    Interrupt Handling State

    Dedicated state for handling voice interrupts during TTS playback.
    Works with StateManager to coordinate interrupt detection, confirmation,
    and appropriate response generation based on action reversibility.
    """

    def __init__(self, state_id: str, agent_registry, session_id: str, interrupt_config=None):
        super().__init__(state_id, agent_registry, session_id)

        # Interrupt configuration
        self.interrupt_config = interrupt_config
        if interrupt_config is not None:
            self.vad_threshold = interrupt_config.detection.vad_threshold
            self.confirmation_window = interrupt_config.detection.confirmation_window_seconds
            self.min_interrupt_duration = interrupt_config.detection.min_interrupt_duration_seconds
        else:
            # Default fallback values
            self.vad_threshold = 0.01
            self.confirmation_window = 0.5
            self.min_interrupt_duration = 0.3

        # Interrupt detection state
        self.interrupt_detected = False
        self.interrupt_confirmed = False
        self.user_input_during_interrupt = None

        # Action reversibility messages
        self.interrupt_messages = {
            "reversible": "Allow me to finish this first, then I'll respond to what you said.",
            "irreversible": "The action has already been completed. If something went wrong, let me know and I'll help fix it.",
            "unknown": "I understand you want to say something. Allow me to finish this first, then I'll respond to what you said."
        }

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        """
        Process interrupt detection and handling.

        Expected input_data:
        - audio_data: Raw audio data for VAD analysis
        - current_tts_audio_path: Path to currently playing TTS audio
        - playback_position: Current position in TTS playback
        - action_context: Context for reversibility analysis
        """
        try:
            self.logger.info(
                "Processing interrupt detection",
                action="process",
                input_data={"has_audio_data": "audio_data" in input_data},
                layer="interrupt_state"
            )

            # Step 1: Detect voice activity
            voice_detected = await self._detect_voice_activity(input_data.get("audio_data"))

            if not voice_detected:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="No voice activity detected",
                    code=StatusCode.OK,
                    outputs={"interrupt_detected": False, "voice_activity": False},
                    meta={"interrupt_state": "no_voice"}
                )

            # Step 2: Confirm interrupt with grace period
            interrupt_confirmed = await self._confirm_interrupt_with_grace_period()

            if not interrupt_confirmed:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="Voice detected but not confirmed as interrupt",
                    code=StatusCode.OK,
                    outputs={"interrupt_detected": False, "voice_activity": True, "confirmed": False},
                    meta={"interrupt_state": "false_alarm"}
                )

            # Step 3: Analyze action reversibility
            action_reversible = self._analyze_action_reversibility(context)

            # Step 4: Generate appropriate acknowledgment
            acknowledgment = self.interrupt_messages.get(action_reversible, self.interrupt_messages["unknown"])

            # Step 5: Store interrupt context in memory
            await self._store_interrupt_context(input_data, action_reversible, acknowledgment, context)

            # Step 6: Return interrupt handling results
            result_outputs = {
                "interrupt_detected": True,
                "interrupt_confirmed": True,
                "voice_activity": True,
                "action_reversible": action_reversible,
                "acknowledgment_message": acknowledgment,
                "should_resume_tts": action_reversible in ["reversible", "unknown"],
                "should_queue_input": action_reversible in ["reversible", "unknown"],
                "playback_position": input_data.get("playback_position", 0.0),
                "current_tts_audio_path": input_data.get("current_tts_audio_path")
            }

            await self._publish_notification("complete", result_outputs, list(result_outputs.keys()))

            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt detected and handled successfully",
                code=StatusCode.OK,
                outputs=result_outputs,
                meta={"interrupt_state": "handled", "action_reversible": action_reversible}
            )

        except Exception as e:
            self.logger.error(
                "Error processing interrupt",
                action="process",
                reason=str(e),
                layer="interrupt_state"
            )
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt processing error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def _detect_voice_activity(self, audio_data: bytes) -> bool:
        """
        Detect voice activity in audio data using VAD.

        Returns:
            bool: True if voice activity detected, False otherwise
        """
        if not audio_data:
            return False

        try:
            from utils.audio_utils import AudioProcessor

            audio_processor = AudioProcessor(self.interrupt_config)
            vad_result = audio_processor.detect_voice_activity(
                audio_data,
                threshold=self.vad_threshold,
                session_id=self.session_id
            )

            if vad_result.status == StatusType.SUCCESS:
                has_voice = vad_result.meta.get("has_voice", False)
                self.logger.info(
                    "Voice activity detection completed",
                    action="_detect_voice_activity",
                    output_data={"has_voice": has_voice},
                    layer="interrupt_state"
                )
                return has_voice
            else:
                self.logger.warning(
                    "Voice activity detection failed",
                    action="_detect_voice_activity",
                    reason=vad_result.message,
                    layer="interrupt_state"
                )
                return False

        except Exception as e:
            self.logger.error(
                "Error in voice activity detection",
                action="_detect_voice_activity",
                reason=str(e),
                layer="interrupt_state"
            )
            return False

    async def _confirm_interrupt_with_grace_period(self) -> bool:
        """
        Confirm interrupt with grace period to avoid false alarms.

        Returns:
            bool: True if interrupt confirmed, False if false alarm
        """
        try:
            self.logger.info(
                f"Starting interrupt confirmation with {self.confirmation_window}s grace period",
                action="_confirm_interrupt_with_grace_period",
                layer="interrupt_state"
            )

            # Wait for confirmation window
            await asyncio.sleep(self.confirmation_window)

            # In a real implementation, this would:
            # 1. Continue monitoring audio input during grace period
            # 2. Confirm that voice activity continues
            # 3. Check for minimum duration requirements

            # For now, we'll simulate confirmation (in real implementation,
            # this would involve continued VAD monitoring)
            confirmed = True

            self.logger.info(
                "Interrupt confirmation completed",
                action="_confirm_interrupt_with_grace_period",
                output_data={"confirmed": confirmed},
                layer="interrupt_state"
            )

            return confirmed

        except Exception as e:
            self.logger.error(
                "Error in interrupt confirmation",
                action="_confirm_interrupt_with_grace_period",
                reason=str(e),
                layer="interrupt_state"
            )
            return False

    def _analyze_action_reversibility(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Analyze action reversibility from context configuration.

        Returns:
            str: "reversible", "irreversible", or "unknown"
        """
        if not context:
            return "unknown"

        # Try to get from workflow state config
        state_config = context.get("workflow_state_config") or context.get("state_config")
        if state_config:
            explicit_reversibility = state_config.get("reversible")
            if explicit_reversibility is not None:
                return "reversible" if explicit_reversibility else "irreversible"

        # Check for explicit reversibility in context
        explicit_reversibility = context.get("explicit_reversibility")
        if explicit_reversibility is not None:
            return "reversible" if explicit_reversibility else "irreversible"

        return "unknown"

    async def _store_interrupt_context(self, input_data: Dict[str, Any], action_reversible: str,
                                     acknowledgment: str, context: Optional[Dict[str, Any]] = None):
        """
        Store interrupt context in memory for StateManager coordination.
        """
        try:
            from datetime import datetime

            # Get memory manager
            memory_manager = await self._get_memory_manager(context)
            if memory_manager:
                # Store interrupt context
                await memory_manager.set_interrupt_context(
                    detected=True,
                    confirmed=True,
                    user_input_queued=input_data.get("user_input", "[User interrupted during TTS]"),
                    resume_after_acknowledgment=action_reversible in ["reversible", "unknown"],
                    action_reversible=action_reversible != "irreversible",
                    interrupt_timestamp=datetime.now().isoformat()
                )

                # Store TTS playback state for resume
                current_audio_path = input_data.get("current_tts_audio_path")
                playback_position = input_data.get("playback_position", 0.0)

                if current_audio_path:
                    import hashlib
                    message_hash = hashlib.sha256(current_audio_path.encode()).hexdigest()
                    await memory_manager.set_tts_playback_state(
                        audio_path=current_audio_path,
                        status="interrupted",
                        playback_position=playback_position,
                        message_hash=message_hash
                    )

                # Log interrupt event
                await memory_manager.add_interrupt_event(
                    event_type="interruption_detected",
                    details={
                        "action_reversible": action_reversible,
                        "acknowledgment": acknowledgment,
                        "playback_position": playback_position,
                        "audio_path": current_audio_path,
                        "timestamp": datetime.now().isoformat()
                    }
                )

                self.logger.info(
                    "Interrupt context stored in memory",
                    action="_store_interrupt_context",
                    output_data={"context_stored": True},
                    layer="interrupt_state"
                )

        except Exception as e:
            self.logger.error(
                "Error storing interrupt context",
                action="_store_interrupt_context",
                reason=str(e),
                layer="interrupt_state"
            )

    async def _get_memory_manager(self, context: Optional[Dict[str, Any]] = None):
        """Get memory manager from various sources."""
        memory_manager = getattr(self, 'memory_manager', None)
        if memory_manager is None and context is not None:
            memory_manager = context.get('memory_manager')
        if memory_manager is None and hasattr(self.agent_registry, 'memory_manager'):
            memory_manager = self.agent_registry.memory_manager
        return memory_manager